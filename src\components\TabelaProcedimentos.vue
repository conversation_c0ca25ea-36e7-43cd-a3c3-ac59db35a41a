<template>
  <div class="tabela-procedimentos">
    <!-- Header com título e botões de ação -->
    <div class="card shadow-sm">
      <div class="card-header bg-gradient-primary text-white">
        <div class="row align-items-center">
          <div class="col">
            <h4 class="mb-0">
              <i class="fas fa-list-alt me-2"></i>
              Tabela de Procedimentos
            </h4>
            <small class="opacity-8">Gerencie os procedimentos, serviços e produtos da clínica</small>
          </div>
          <div class="col-auto">
            <button 
              class="btn btn-light btn-sm me-2"
              @click="openCategoriaModal()"
              title="Gerenciar Categorias"
            >
              <i class="fas fa-tags me-1"></i>
              Categorias
            </button>
            <button 
              class="btn btn-success btn-sm"
              @click="openProcedimentoModal()"
              title="Adicionar Procedimento"
            >
              <i class="fas fa-plus me-1"></i>
              Novo Procedimento
            </button>
          </div>
        </div>
      </div>

      <div class="card-body p-0">
        <!-- Filtros -->
        <div class="p-3 border-bottom bg-light">
          <div class="row g-3">
            <div class="col-md-4">
              <div class="input-group input-group-sm">
                <span class="input-group-text">
                  <i class="fas fa-search"></i>
                </span>
                <input
                  type="text"
                  class="form-control"
                  placeholder="Buscar procedimentos..."
                  v-model="filtros.busca"
                  @input="buscarProcedimentos"
                >
              </div>
            </div>
            <div class="col-md-3">
              <select 
                class="form-select form-select-sm"
                v-model="filtros.categoria_id"
                @change="buscarProcedimentos"
              >
                <option value="">Todas as categorias</option>
                <option 
                  v-for="categoria in categorias" 
                  :key="categoria.id" 
                  :value="categoria.id"
                >
                  {{ categoria.nome }}
                </option>
              </select>
            </div>
            <div class="col-md-2">
              <select 
                class="form-select form-select-sm"
                v-model="filtros.tipo"
                @change="buscarProcedimentos"
              >
                <option value="">Todos os tipos</option>
                <option value="procedimento">Procedimentos</option>
                <option value="servico">Serviços</option>
                <option value="produto">Produtos</option>
              </select>
            </div>
            <div class="col-md-2">
              <select 
                class="form-select form-select-sm"
                v-model="filtros.ativo"
                @change="buscarProcedimentos"
              >
                <option value="">Todos</option>
                <option value="1">Ativos</option>
                <option value="0">Inativos</option>
              </select>
            </div>
            <div class="col-md-1">
              <button 
                class="btn btn-outline-secondary btn-sm w-100"
                @click="limparFiltros"
                title="Limpar filtros"
              >
                <i class="fas fa-times"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- Loading -->
        <div v-if="loading" class="text-center p-4">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Carregando...</span>
          </div>
        </div>

        <!-- Tabela de procedimentos -->
        <div v-else-if="procedimentos.length > 0" class="table-responsive">
          <table class="table table-hover mb-0">
            <thead class="table-light">
              <tr>
                <th style="width: 50px;">Status</th>
                <th style="width: 80px;">Código</th>
                <th>Nome</th>
                <th style="width: 120px;">Categoria</th>
                <th style="width: 100px;">Tipo</th>
                <th style="width: 120px;">Valor Base</th>
                <th style="width: 80px;">Unidade</th>
                <th style="width: 100px;">Tempo</th>
                <th style="width: 120px;">Ações</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="procedimento in procedimentos" :key="procedimento.id">
                <td>
                  <div class="form-check form-switch">
                    <input 
                      class="form-check-input" 
                      type="checkbox" 
                      :checked="procedimento.ativo"
                      @change="toggleStatus(procedimento)"
                      :disabled="toggleLoading[procedimento.id]"
                    >
                  </div>
                </td>
                <td>
                  <code class="text-muted small">{{ procedimento.codigo || '-' }}</code>
                </td>
                <td>
                  <div>
                    <strong>{{ procedimento.nome }}</strong>
                    <div v-if="procedimento.descricao" class="text-muted small">
                      {{ procedimento.descricao.substring(0, 50) }}{{ procedimento.descricao.length > 50 ? '...' : '' }}
                    </div>
                  </div>
                </td>
                <td>
                  <span 
                    v-if="procedimento.categoria"
                    class="badge rounded-pill"
                    :style="{ backgroundColor: procedimento.categoria.cor, color: '#fff' }"
                  >
                    {{ procedimento.categoria.nome }}
                  </span>
                  <span v-else class="text-muted">-</span>
                </td>
                <td>
                  <span class="badge" :class="getTipoBadgeClass(procedimento.tipo)">
                    <i :class="getTipoIcon(procedimento.tipo)" class="me-1"></i>
                    {{ getTipoLabel(procedimento.tipo) }}
                  </span>
                </td>
                <td>
                  <strong class="text-success">{{ formatCurrency(procedimento.valor_base) }}</strong>
                  <div v-if="procedimento.valor_minimo || procedimento.valor_maximo" class="text-muted small">
                    {{ getFaixaValor(procedimento) }}
                  </div>
                </td>
                <td>
                  <span class="badge bg-light text-dark">{{ procedimento.unidade }}</span>
                </td>
                <td>
                  <span v-if="procedimento.tempo_estimado" class="text-muted small">
                    {{ formatTempo(procedimento.tempo_estimado) }}
                  </span>
                  <span v-else class="text-muted">-</span>
                </td>
                <td>
                  <div class="btn-group btn-group-sm">
                    <button 
                      class="btn btn-outline-primary btn-sm"
                      @click="openProcedimentoModal(procedimento)"
                      title="Editar"
                    >
                      <i class="fas fa-edit"></i>
                    </button>
                    <button 
                      class="btn btn-outline-danger btn-sm"
                      @click="confirmarExclusao(procedimento)"
                      title="Excluir"
                    >
                      <i class="fas fa-trash"></i>
                    </button>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </div>

        <!-- Estado vazio -->
        <div v-else class="text-center p-5">
          <i class="fas fa-list-alt fa-3x text-muted mb-3"></i>
          <h5 class="text-muted">Nenhum procedimento encontrado</h5>
          <p class="text-muted">
            {{ filtros.busca || filtros.categoria_id || filtros.tipo ? 
               'Tente ajustar os filtros de busca.' : 
               'Comece adicionando seu primeiro procedimento.' }}
          </p>
          <button 
            v-if="!filtros.busca && !filtros.categoria_id && !filtros.tipo"
            class="btn btn-primary"
            @click="openProcedimentoModal()"
          >
            <i class="fas fa-plus me-1"></i>
            Adicionar Primeiro Procedimento
          </button>
        </div>

        <!-- Paginação -->
        <div v-if="paginacao && paginacao.last_page > 1" class="p-3 border-top">
          <nav>
            <ul class="pagination pagination-sm justify-content-center mb-0">
              <li class="page-item" :class="{ disabled: paginacao.current_page === 1 }">
                <button class="page-link" @click="irParaPagina(paginacao.current_page - 1)">
                  <i class="fas fa-chevron-left"></i>
                </button>
              </li>
              <li 
                v-for="pagina in getPaginasVisiveis()" 
                :key="pagina"
                class="page-item" 
                :class="{ active: pagina === paginacao.current_page }"
              >
                <button class="page-link" @click="irParaPagina(pagina)">
                  {{ pagina }}
                </button>
              </li>
              <li class="page-item" :class="{ disabled: paginacao.current_page === paginacao.last_page }">
                <button class="page-link" @click="irParaPagina(paginacao.current_page + 1)">
                  <i class="fas fa-chevron-right"></i>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </div>
    </div>

    <!-- Modal de Procedimento -->
     <teleport to="body">
      <ProcedimentoModal 
      :append-to-body="true"
        ref="procedimentoModal"
        :categorias="categorias"
        @saved="onProcedimentoSaved"
      />

      <!-- Modal de Categorias -->
      <CategoriaModal 
      :append-to-body="true"
        ref="categoriaModal"
        @saved="onCategoriaSaved"
      />
    </teleport>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import ProcedimentoModal from './modals/ProcedimentoModal.vue';
import CategoriaModal from './modals/CategoriaModal.vue';
import cSwal from '@/utils/cSwal';

export default {
  name: 'TabelaProcedimentos',
  components: {
    ProcedimentoModal,
    CategoriaModal,
  },
  data() {
    return {
      loading: false,
      procedimentos: [],
      categorias: [],
      paginacao: null,
      filtros: {
        busca: '',
        categoria_id: '',
        tipo: '',
        ativo: '',
        page: 1
      },
      toggleLoading: {},
      buscarTimeout: null
    };
  },
  async mounted() {
    await this.carregarDados();
  },
  methods: {
    async carregarDados() {
      await Promise.all([
        this.carregarProcedimentos(),
        this.carregarCategorias()
      ]);
    },

    async carregarProcedimentos() {
      this.loading = true;
      try {
        const response = await servicoProdutoService.getServicosProdutos(this.filtros);
        this.procedimentos = response.data.data.data || [];
        this.paginacao = {
          current_page: response.data.data.current_page,
          last_page: response.data.data.last_page,
          per_page: response.data.data.per_page,
          total: response.data.data.total
        };
      } catch (error) {
        console.error('Erro ao carregar procedimentos:', error);
        cSwal.cError('Erro ao carregar procedimentos');
      } finally {
        this.loading = false;
      }
    },

    async carregarCategorias() {
      try {
        const response = await servicoProdutoService.getCategorias();
        this.categorias = response.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
      }
    },

    buscarProcedimentos() {
      // Debounce para evitar muitas requisições
      clearTimeout(this.buscarTimeout);
      this.buscarTimeout = setTimeout(() => {
        this.filtros.page = 1;
        this.carregarProcedimentos();
      }, 300);
    },

    limparFiltros() {
      this.filtros = {
        busca: '',
        categoria_id: '',
        tipo: '',
        ativo: '',
        page: 1
      };
      this.carregarProcedimentos();
    },

    irParaPagina(pagina) {
      if (pagina >= 1 && pagina <= this.paginacao.last_page) {
        this.filtros.page = pagina;
        this.carregarProcedimentos();
      }
    },

    getPaginasVisiveis() {
      const current = this.paginacao.current_page;
      const last = this.paginacao.last_page;
      const delta = 2;
      const range = [];
      
      for (let i = Math.max(2, current - delta); i <= Math.min(last - 1, current + delta); i++) {
        range.push(i);
      }
      
      if (current - delta > 2) {
        range.unshift('...');
      }
      if (current + delta < last - 1) {
        range.push('...');
      }
      
      range.unshift(1);
      if (last !== 1) {
        range.push(last);
      }
      
      return range.filter((item, index, arr) => arr.indexOf(item) === index);
    },

    async toggleStatus(procedimento) {
      this.$set(this.toggleLoading, procedimento.id, true);
      try {
        await servicoProdutoService.toggleStatus(procedimento.id);
        procedimento.ativo = !procedimento.ativo;
        cSwal.cSuccess(`Procedimento ${procedimento.ativo ? 'ativado' : 'desativado'} com sucesso`);
      } catch (error) {
        console.error('Erro ao alterar status:', error);
        cSwal.cError('Erro ao alterar status do procedimento');
      } finally {
        this.$set(this.toggleLoading, procedimento.id, false);
      }
    },

    openProcedimentoModal(procedimento = null) {
      this.$refs.procedimentoModal.open(procedimento);
    },

    openCategoriaModal() {
      this.$refs.categoriaModal.open();
    },

    onProcedimentoSaved() {
      this.carregarProcedimentos();
    },

    onCategoriaSaved() {
      this.carregarCategorias();
    },

    async confirmarExclusao(procedimento) {
      const result = await cSwal.cConfirm(
        `Tem certeza que deseja excluir o procedimento "${procedimento.nome}"?`,
        'Esta ação não pode ser desfeita.',
        'Sim, excluir',
        'Cancelar'
      );

      if (result.isConfirmed) {
        try {
          await servicoProdutoService.deleteServicoProduto(procedimento.id);
          cSwal.cSuccess('Procedimento excluído com sucesso');
          this.carregarProcedimentos();
        } catch (error) {
          console.error('Erro ao excluir procedimento:', error);
          cSwal.cError('Erro ao excluir procedimento');
        }
      }
    },

    // Métodos de formatação
    formatCurrency(value) {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value);
    },

    formatTempo(minutos) {
      if (minutos < 60) {
        return `${minutos}min`;
      }
      const horas = Math.floor(minutos / 60);
      const mins = minutos % 60;
      return mins > 0 ? `${horas}h ${mins}min` : `${horas}h`;
    },

    getFaixaValor(procedimento) {
      if (procedimento.valor_minimo && procedimento.valor_maximo) {
        return `${this.formatCurrency(procedimento.valor_minimo)} - ${this.formatCurrency(procedimento.valor_maximo)}`;
      }
      return '';
    },

    getTipoLabel(tipo) {
      const labels = {
        'servico': 'Serviço',
        'produto': 'Produto',
        'procedimento': 'Procedimento'
      };
      return labels[tipo] || tipo;
    },

    getTipoIcon(tipo) {
      const icons = {
        'servico': 'fas fa-handshake',
        'produto': 'fas fa-box',
        'procedimento': 'fas fa-user-md'
      };
      return icons[tipo] || 'fas fa-tag';
    },

    getTipoBadgeClass(tipo) {
      const classes = {
        'servico': 'bg-info',
        'produto': 'bg-warning',
        'procedimento': 'bg-primary'
      };
      return classes[tipo] || 'bg-secondary';
    }
  }
};
</script>

<style scoped>
.tabela-procedimentos {
  min-height: 600px;
}

.card-header {
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.table th {
  font-weight: 600;
  font-size: 0.875rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.form-switch .form-check-input {
  width: 2.5em;
  height: 1.25em;
}

.badge {
  font-size: 0.75rem;
}

.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.pagination-sm .page-link {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.input-group-sm .input-group-text {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.form-select-sm {
  padding: 0.25rem 0.5rem;
  font-size: 0.875rem;
}

.bg-gradient-primary {
  background: linear-gradient(87deg, #5e72e4 0, #825ee4 100%) !important;
}
</style>
