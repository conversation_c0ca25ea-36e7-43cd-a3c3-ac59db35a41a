<template>
  <div class="modal fade" id="categoriaModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
      <div class="modal-content">
        <div class="modal-header bg-gradient-light">
          <h5 class="modal-title text-dark">
            <i class="fas fa-tags me-2"></i>
            Gerenciar Categorias
          </h5>
          <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
        </div>

        <div class="modal-body">
          <!-- Formulário para nova categoria -->
          <div class="card mb-4">
            <div class="card-header bg-light">
              <h6 class="mb-0">
                <i class="fas fa-plus me-1"></i>
                {{ editingCategoria ? 'Editar Categoria' : 'Nova Categoria' }}
              </h6>
            </div>
            <div class="card-body">
              <form @submit.prevent="salvarCategoria">
                <div class="row g-3">
                  <div class="col-md-6">
                    <label class="form-label">Nome *</label>
                    <input
                      type="text"
                      class="form-control"
                      v-model="formCategoria.nome"
                      :class="{ 'is-invalid': errorsCategoria.nome }"
                      placeholder="Nome da categoria"
                      maxlength="255"
                      required
                    >
                    <div v-if="errorsCategoria.nome" class="invalid-feedback">{{ errorsCategoria.nome }}</div>
                  </div>

                  <div class="col-md-6">
                    <label class="form-label">Cor</label>
                    <div class="input-group">
                      <input
                        type="color"
                        class="form-control form-control-color"
                        v-model="formCategoria.cor"
                        :class="{ 'is-invalid': errorsCategoria.cor }"
                        title="Escolha uma cor"
                      >
                      <input
                        type="text"
                        class="form-control"
                        v-model="formCategoria.cor"
                        :class="{ 'is-invalid': errorsCategoria.cor }"
                        placeholder="#007bff"
                        pattern="^#[0-9A-Fa-f]{6}$"
                      >
                    </div>
                    <div v-if="errorsCategoria.cor" class="invalid-feedback">{{ errorsCategoria.cor }}</div>
                  </div>

                  <div class="col-12">
                    <label class="form-label">Descrição</label>
                    <textarea
                      class="form-control"
                      v-model="formCategoria.descricao"
                      :class="{ 'is-invalid': errorsCategoria.descricao }"
                      placeholder="Descrição da categoria"
                      rows="2"
                    ></textarea>
                    <div v-if="errorsCategoria.descricao" class="invalid-feedback">{{ errorsCategoria.descricao }}</div>
                  </div>

                  <div class="col-12">
                    <button type="submit" class="btn btn-primary me-2" :disabled="loadingCategoria">
                      <span v-if="loadingCategoria" class="spinner-border spinner-border-sm me-2"></span>
                      <i v-else class="fas fa-save me-1"></i>
                      {{ loadingCategoria ? 'Salvando...' : (editingCategoria ? 'Atualizar' : 'Criar') }}
                    </button>
                    <button 
                      v-if="editingCategoria" 
                      type="button" 
                      class="btn btn-secondary"
                      @click="cancelarEdicao"
                    >
                      <i class="fas fa-times me-1"></i>
                      Cancelar
                    </button>
                  </div>
                </div>
              </form>
            </div>
          </div>

          <!-- Lista de categorias existentes -->
          <div class="card">
            <div class="card-header bg-light">
              <h6 class="mb-0">
                <i class="fas fa-list me-1"></i>
                Categorias Existentes
              </h6>
            </div>
            <div class="card-body p-0">
              <div v-if="loadingCategorias" class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Carregando...</span>
                </div>
              </div>

              <div v-else-if="categorias.length > 0" class="table-responsive">
                <table class="table table-hover mb-0">
                  <thead class="table-light">
                    <tr>
                      <th style="width: 50px;">Status</th>
                      <th style="width: 60px;">Cor</th>
                      <th>Nome</th>
                      <th>Descrição</th>
                      <th style="width: 100px;">Procedimentos</th>
                      <th style="width: 120px;">Ações</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="categoria in categorias" :key="categoria.id">
                      <td>
                        <div class="form-check form-switch">
                          <input 
                            class="form-check-input" 
                            type="checkbox" 
                            :checked="categoria.ativo"
                            @change="toggleCategoriaStatus(categoria)"
                            :disabled="toggleLoading[categoria.id]"
                          >
                        </div>
                      </td>
                      <td>
                        <div 
                          class="color-preview"
                          :style="{ backgroundColor: categoria.cor }"
                          :title="categoria.cor"
                        ></div>
                      </td>
                      <td>
                        <strong>{{ categoria.nome }}</strong>
                      </td>
                      <td>
                        <span class="text-muted">{{ categoria.descricao || '-' }}</span>
                      </td>
                      <td>
                        <span class="badge bg-info">
                          {{ categoria.quantidade_servicos || 0 }}
                        </span>
                      </td>
                      <td>
                        <div class="btn-group btn-group-sm">
                          <button 
                            class="btn btn-outline-primary btn-sm"
                            @click="editarCategoria(categoria)"
                            title="Editar"
                          >
                            <i class="fas fa-edit"></i>
                          </button>
                          <button 
                            class="btn btn-outline-danger btn-sm"
                            @click="confirmarExclusaoCategoria(categoria)"
                            title="Excluir"
                            :disabled="categoria.quantidade_servicos > 0"
                          >
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <div v-else class="text-center p-4">
                <i class="fas fa-tags fa-2x text-muted mb-3"></i>
                <h6 class="text-muted">Nenhuma categoria encontrada</h6>
                <p class="text-muted small">Crie sua primeira categoria usando o formulário acima.</p>
              </div>
            </div>
          </div>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
            <i class="fas fa-times me-1"></i>
            Fechar
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { servicoProdutoService } from '@/services/servicoProdutoService';
import { openModal } from '@/utils/modalHelper';
import cSwal from '@/utils/cSwal';

export default {
  name: 'CategoriaModal',
  data() {
    return {
      categorias: [],
      loadingCategorias: false,
      loadingCategoria: false,
      editingCategoria: null,
      formCategoria: {
        nome: '',
        descricao: '',
        cor: '#007bff'
      },
      errorsCategoria: {},
      toggleLoading: {}
    };
  },
  methods: {
    open() {
      this.resetForm();
      this.carregarCategorias();
      openModal('categoriaModal');
    },

    resetForm() {
      this.editingCategoria = null;
      this.formCategoria = {
        nome: '',
        descricao: '',
        cor: '#007bff'
      };
      this.errorsCategoria = {};
    },

    async carregarCategorias() {
      this.loadingCategorias = true;
      try {
        const response = await servicoProdutoService.getCategorias();
        this.categorias = response.data.data || [];
      } catch (error) {
        console.error('Erro ao carregar categorias:', error);
        cSwal.cError('Erro ao carregar categorias');
      } finally {
        this.loadingCategorias = false;
      }
    },

    validateForm() {
      this.errorsCategoria = {};
      let isValid = true;

      if (!this.formCategoria.nome || this.formCategoria.nome.trim() === '') {
        this.errorsCategoria.nome = 'O nome é obrigatório';
        isValid = false;
      }

      if (!this.formCategoria.cor || !/^#[0-9A-Fa-f]{6}$/.test(this.formCategoria.cor)) {
        this.errorsCategoria.cor = 'Cor inválida (formato: #RRGGBB)';
        isValid = false;
      }

      return isValid;
    },

    async salvarCategoria() {
      if (!this.validateForm()) {
        return;
      }

      this.loadingCategoria = true;
      this.errorsCategoria = {};

      try {
        const data = { ...this.formCategoria };

        let response;
        if (this.editingCategoria) {
          response = await servicoProdutoService.updateCategoria(this.editingCategoria.id, data);
        } else {
          response = await servicoProdutoService.createCategoria(data);
        }

        cSwal.cSuccess(
          this.editingCategoria ? 'Categoria atualizada com sucesso!' : 'Categoria criada com sucesso!'
        );

        this.resetForm();
        this.carregarCategorias();
        this.$emit('saved');

      } catch (error) {
        console.error('Erro ao salvar categoria:', error);
        
        if (error.response && error.response.data && error.response.data.data) {
          this.errorsCategoria = error.response.data.data;
        } else {
          cSwal.cError('Erro ao salvar categoria');
        }
      } finally {
        this.loadingCategoria = false;
      }
    },

    editarCategoria(categoria) {
      this.editingCategoria = categoria;
      this.formCategoria = {
        nome: categoria.nome,
        descricao: categoria.descricao || '',
        cor: categoria.cor || '#007bff'
      };
      this.errorsCategoria = {};
    },

    cancelarEdicao() {
      this.resetForm();
    },

    async toggleCategoriaStatus(categoria) {
      this.toggleLoading[categoria.id] = true;
      try {
        await servicoProdutoService.toggleCategoriaStatus(categoria.id);
        categoria.ativo = !categoria.ativo;
        cSwal.cSuccess(`Categoria ${categoria.ativo ? 'ativada' : 'desativada'} com sucesso`);
        this.$emit('saved');
      } catch (error) {
        console.error('Erro ao alterar status da categoria:', error);
        cSwal.cError('Erro ao alterar status da categoria');
      } finally {
        this.toggleLoading[categoria.id] = false;
      }
    },

    async confirmarExclusaoCategoria(categoria) {
      if (categoria.quantidade_servicos > 0) {
        cSwal.cError('Esta categoria não pode ser excluída pois possui procedimentos associados');
        return;
      }

      cSwal.cConfirm(
        `Tem certeza que deseja excluir a categoria "<strong>${categoria.nome}</strong>"?<br><small class="text-muted">Esta ação não pode ser desfeita.</small>`,
        async () => {
          try {
            await servicoProdutoService.deleteCategoria(categoria.id);
            cSwal.cSuccess('Categoria excluída com sucesso');
            this.carregarCategorias();
            this.$emit('saved');
          } catch (error) {
            console.error('Erro ao excluir categoria:', error);
            cSwal.cError('Erro ao excluir categoria');
          }
        },
        {
          confirmButtonText: 'Sim, excluir',
          cancelButtonText: 'Cancelar',
          confirmButtonColor: '#dc3545'
        }
      );
    }
  }
};
</script>

<style scoped>
.modal-header {
  border-radius: 0.5rem 0.5rem 0 0 !important;
}

.bg-gradient-light {
  background: linear-gradient(87deg, #f8f9fc 0, #e9ecef 100%) !important;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.form-control, .form-select {
  border-radius: 0.375rem;
  border: 1px solid #d1d3e2;
  transition: all 0.15s ease-in-out;
}

.form-control:focus, .form-select:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-control-color {
  width: 60px;
  height: 38px;
  padding: 0.375rem 0.5rem;
}

.color-preview {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
}

.card-header {
  background-color: #f8f9fc !important;
  border-bottom: 1px solid #e3e6f0;
}

.card-header h6 {
  color: #5a5c69;
  font-weight: 600;
}

.table th {
  font-weight: 600;
  font-size: 0.875rem;
  color: #495057;
  border-bottom: 2px solid #dee2e6;
}

.table td {
  vertical-align: middle;
  font-size: 0.875rem;
}

.form-switch .form-check-input {
  width: 2.5em;
  height: 1.25em;
}

.badge {
  font-size: 0.75rem;
}

.btn-group-sm > .btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
}

.btn-primary {
  background: linear-gradient(87deg, #007bff 0, #0056b3 100%);
  border-color: #007bff;
}

.btn-primary:hover {
  background: linear-gradient(87deg, #0056b3 0, #004085 100%);
  border-color: #0056b3;
}
</style>
