<template>
  <div>
    <div class="p-horizontal-divider my-0 d-none d-md-block" ref="metasTerapeuticasFraming"></div>

      <Transition name="carousel-fade">
        <div v-if="imagens.length > 0"
          class="p-1 flex-row img-carousel-container d-none d-md-flex"
          v-viewer="{ title: [1, (image, imageData) => `${image.alt}`], hide: resumeCarousel }"
          ref="carouselViewer"
          :class="{ 'dimmed-carousel': planejamentoTab === 'imagens' }"
        >
        <div class="carousel-controls">
          <button class="carousel-control" @click="toggleCarousel" :title="isPaused ? 'Iniciar' : 'Pausar'">
            <font-awesome-icon :icon="['fas', isPaused ? 'play' : 'pause']" />
          </button>
          <button class="carousel-control" @click="toggleCarouselVisibility" :title="isCarouselHidden ? 'Mostrar' : 'Esconder'">
            <font-awesome-icon :icon="['fas', isCarouselHidden ? 'chevron-down' : 'chevron-up']" />
          </button>
        </div>
        <!-- Botão para restaurar o carousel quando estiver escondido -->
        <Transition name="fade">
          <div v-if="isCarouselHidden" class="carousel-restore-container">
            <button class="carousel-restore-button" @click="toggleCarouselVisibility" title="Mostrar documentação">
              <font-awesome-icon :icon="['fas', 'images']" class="me-2" />
              Mostrar documentação
            </button>
          </div>
        </Transition>

        <div
          class="img-carousel-inner"
          :style="{
            animationPlayState: isPaused ? 'paused' : 'running',
            display: isCarouselHidden ? 'none' : 'flex'
          }"
        >
          <div
            v-for="imagem in imagens"
            :key="imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
          <div
            v-for="imagem in imagens"
            :key="'dup-' + imagem.url"
            class="img-carousel-item"
          >
            <img
              :src="imagem.url"
              :alt="getImageDescription(imagem)"
              :title="getImageDescription(imagem)"
              @click="pauseCarousel"
            />
            <div class="img-carousel-info">
              <span class="img-time">{{ $filters.howMuchTime(imagem.data, { type: 'date' }) }}</span>
              <span class="img-desc" v-if="imagem.descricao">{{ truncateDescription(imagem.descricao, 50) }}</span>
            </div>
          </div>
        </div>
      </div>
      </Transition>

    <div class="my-0" ref="metasTerapeuticasFraming" style="margin-top: 0px !important; background:rgb(228, 228, 228); height: 1px;"></div>

    <!-- Barra de Progresso Estilizada -->
    <div class="progress-navigation-wrapper mb-4">
      <div class="progress-navigation">
        <!-- Linha de progresso de fundo -->
        <div class="progress-line">
          <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
        </div>

        <!-- Etapas do planejamento -->
        <div class="progress-steps">
          <!-- Documentação Inicial -->
          <div class="progress-step"
               :class="getStepClasses('imagens')"
               @click="selectPlanejamentoTab('imagens')">
            <div class="step-circle">
              <div class="step-icon-wrapper">
                <font-awesome-icon
                  :icon="getStepIcon('imagens')"
                  class="step-icon"
                />
              </div>
              <div class="step-pulse" v-if="planejamentoTab === 'imagens'"></div>
            </div>
            <div class="step-label">
              <span class="step-title">Documentação</span>
              <span class="step-subtitle">inicial</span>
            </div>
          </div>

          <!-- Análise -->
          <div class="progress-step"
               :class="getStepClasses('analise')"
               @click="canAccessStep('analise') && selectPlanejamentoTab('analise')">
            <div class="step-circle">
              <div class="step-icon-wrapper">
                <font-awesome-icon
                  :icon="getStepIcon('analise')"
                  class="step-icon"
                />
              </div>
              <div class="step-pulse" v-if="planejamentoTab === 'analise'"></div>
            </div>
            <div class="step-label">
              <span class="step-title">Análise</span>
              <span class="step-subtitle">clínica</span>
            </div>
          </div>

          <!-- Diagnóstico -->
          <div class="progress-step"
               :class="getStepClasses('diagnostico')"
               @click="canAccessStep('diagnostico') && selectPlanejamentoTab('diagnostico')">
            <div class="step-circle">
              <div class="step-icon-wrapper">
                <font-awesome-icon
                  :icon="getStepIcon('diagnostico')"
                  class="step-icon"
                />
              </div>
              <div class="step-pulse" v-if="planejamentoTab === 'diagnostico'"></div>
            </div>
            <div class="step-label">
              <span class="step-title">Diagnóstico</span>
              <span class="step-subtitle">médico</span>
            </div>
          </div>

          <!-- Plano de Tratamento -->
          <div class="progress-step"
               :class="getStepClasses('planoTratamento')"
               @click="canAccessStep('planoTratamento') && selectPlanejamentoTab('planoTratamento')">
            <div class="step-circle">
              <div class="step-icon-wrapper">
                <font-awesome-icon
                  :icon="getStepIcon('planoTratamento')"
                  class="step-icon"
                />
              </div>
              <div class="step-pulse" v-if="planejamentoTab === 'planoTratamento'"></div>
            </div>
            <div class="step-label">
              <span class="step-title">Plano de</span>
              <span class="step-subtitle">tratamento</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="p-horizontal-divider"></div>

    <Transition>
      <Imagens
        v-show="planejamentoTab === 'imagens'"
        :paciente="paciente"
        mode="diagnostic"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Analise
        v-if="planejamentoTab === 'analise'"
        :paciente="paciente"
        :detalhesClinicos="detalhesClinicos"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <Diagnostico
        v-show="planejamentoTab === 'diagnostico'"
        :paciente="paciente"
        :diagnostico="paciente.diagnostico"
        :prognostico="paciente.prognostico"
        @pacienteChange="$emit('pacienteChange')"
        @selectTab="selectPlanejamentoTab"
      />
    </Transition>

    <Transition>
      <PlanoTratamento
        v-show="planejamentoTab === 'planoTratamento'"
        :paciente="paciente"
        @pacienteChange="$emit('pacienteChange')"
        @edit-mode-active="handleEditModeActive"
      />
    </Transition>


  </div>
</template>

<style scoped>
/* Transition effects */
.v-enter-active,
.v-leave-active {
  transition: opacity 0.3s ease;
}

.v-enter-from,
.v-leave-to {
  opacity: 0;
}

/* Estilos da Barra de Progresso */
.progress-navigation-wrapper {
  padding: 12px 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 12px;
  margin: 8px 0;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06);
}

.progress-navigation {
  position: relative;
  max-width: 900px;
  margin: 0 auto;
  padding: 0 20px;
}

.progress-line {
  position: absolute;
  top: 50%;
  left: 30px;
  right: 30px;
  height: 24px;
  transform: translateY(-50%);
  z-index: 1;
  display: flex;
}

/* Segmentos da barra com formato de seta elegante */
.progress-line::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #e9ecef;
  clip-path: polygon(
    0% 0%,
    calc(33.33% - 12px) 0%,
    33.33% 50%,
    calc(33.33% - 12px) 100%,
    calc(66.66% - 12px) 100%,
    66.66% 50%,
    calc(66.66% - 12px) 0%,
    calc(100% - 12px) 0%,
    100% 50%,
    calc(100% - 12px) 100%,
    100% 100%,
    calc(100% + 12px) 50%,
    100% 0%
  );
  border-radius: 12px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #4CAF50, #66BB6A, #81C784);
  transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  clip-path: polygon(
    0% 0%,
    calc(33.33% - 12px) 0%,
    33.33% 50%,
    calc(33.33% - 12px) 100%,
    calc(66.66% - 12px) 100%,
    66.66% 50%,
    calc(66.66% - 12px) 0%,
    calc(100% - 12px) 0%,
    100% 50%,
    calc(100% - 12px) 100%,
    100% 100%,
    calc(100% + 12px) 50%,
    100% 0%
  );
  border-radius: 12px;
}

.progress-fill::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: shimmer 2.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

.progress-steps {
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  z-index: 2;
}

.progress-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  padding: 8px;
  border-radius: 12px;
  position: relative;
}

.progress-step:hover:not(.disabled) {
  transform: translateY(-3px);
}

.progress-step.disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.progress-step.disabled .step-circle {
  background: #e9ecef;
  border-color: #dee2e6;
}

.step-circle {
  position: relative;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: white;
  border: 3px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-bottom: 10px;
  z-index: 3;
}

.progress-step.active .step-circle {
  background: linear-gradient(135deg, #2196F3, #1976D2);
  border-color: #1976D2;
  box-shadow: 0 6px 20px rgba(33, 150, 243, 0.4);
  transform: scale(1.08);
}

.progress-step.completed .step-circle {
  background: linear-gradient(135deg, #4CAF50, #388E3C);
  border-color: #388E3C;
  box-shadow: 0 5px 18px rgba(76, 175, 80, 0.3);
}

.step-icon-wrapper {
  position: relative;
  z-index: 2;
}

.step-icon {
  font-size: 20px;
  color: #6c757d;
  transition: all 0.3s ease;
}

.progress-step.active .step-icon,
.progress-step.completed .step-icon {
  color: white;
  font-size: 22px;
}

.step-pulse {
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  border-radius: 50%;
  border: 2px solid #2196F3;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1.2);
    opacity: 0;
  }
}

.step-label {
  text-align: center;
  transition: all 0.3s ease;
}

.step-title {
  display: block;
  font-weight: 600;
  font-size: 0.9rem;
  color: #495057;
  line-height: 1.2;
}

.step-subtitle {
  display: block;
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 400;
  margin-top: 2px;
}

.progress-step.active .step-title {
  color: #1976D2;
  font-weight: 700;
}

.progress-step.active .step-subtitle {
  color: #2196F3;
}

.progress-step.completed .step-title {
  color: #388E3C;
  font-weight: 600;
}

.progress-step.completed .step-subtitle {
  color: #4CAF50;
}

/* Responsividade */
@media (max-width: 768px) {
  .progress-navigation-wrapper {
    padding: 10px 0;
  }

  .progress-navigation {
    padding: 0 15px;
  }

  .progress-line {
    left: 25px;
    right: 25px;
    height: 20px;
  }

  .step-circle {
    width: 48px;
    height: 48px;
    margin-bottom: 8px;
  }

  .step-icon {
    font-size: 16px;
  }

  .progress-step.active .step-icon,
  .progress-step.completed .step-icon {
    font-size: 18px;
  }

  .step-title {
    font-size: 0.8rem;
  }

  .step-subtitle {
    font-size: 0.7rem;
  }

  .progress-step {
    padding: 6px;
  }
}

.planejamento-content {
  padding: 20px;
  padding-top: 5px;
}

.img-carousel-container {
  overflow-y: hidden;
  overflow-x: hidden; /* hide scrollbar */
  background: #f8f9fa;
  border-width: 0px 1px 0px 1px;
  border-style: solid;
  border-color: #e2e2e2;
  gap: 0px;
  position: relative;
  transition: all 0.3s ease;
  box-shadow: inset 0 0 10px rgba(0, 0, 0, 0.03);
  padding: 10px 0;
}

.img-carousel-container.dimmed-carousel {
  background: rgba(0, 0, 0, 0.03);
  position: relative;
}

.img-carousel-container.dimmed-carousel::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(1px);
  z-index: 5;
  pointer-events: none;
}

.carousel-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  display: flex;
  gap: 5px;
}

.carousel-control {
  background: rgba(255, 255, 255, 0.8);
  border: 1px solid #dee2e6;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #495057;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.carousel-control:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.carousel-restore-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 10px 0;
  min-height: 60px;
}

.carousel-restore-button {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #dee2e6;
  border-radius: 20px;
  padding: 8px 16px;
  font-size: 0.85rem;
  color: #495057;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 10px;
}

.carousel-restore-button:hover {
  background: white;
  transform: translateY(-2px);
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: #2152ff;
}

.img-carousel-inner {
  display: flex;
  flex-wrap: nowrap;
  animation: scroll-left 40s linear infinite;
  padding: 5px 0;
}

.img-carousel-item {
  background: #000;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 8px;
  flex-shrink: 0;
  margin-right: 8px;
  position: relative;
  overflow: hidden;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.img-carousel-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.85), transparent);
  color: white;
  padding: 12px 8px 8px;
  font-size: 0.7rem;
  text-align: center;
  transform: translateY(100%);
  transition: transform 0.3s ease;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.img-carousel-info .img-time {
  font-weight: 600;
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.95);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.img-carousel-info .img-desc {
  font-size: 0.65rem;
  opacity: 0.9;
  font-style: italic;
  line-height: 1.2;
  max-height: 2.4em;
  overflow: hidden;
}

.img-carousel-item:hover .img-carousel-info {
  transform: translateY(0);
}

.img-carousel-item img {
  width: 160px;
  height: auto;
  aspect-ratio: 9/6;
  object-fit: cover;
  filter: brightness(90%);
  transition: all 0.3s ease;
}

.img-carousel-item:hover {
  transform: translateY(-5px) scale(1.03);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
  z-index: 100;
}

.img-carousel-item:hover img {
  filter: brightness(110%);
}

@keyframes scroll-left {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

.empty-photo {
  width: 300px;
  height: 96px;
}

/* Carousel fade transition */
.carousel-fade-enter-active,
.carousel-fade-leave-active {
  transition: opacity 0.5s ease, transform 0.5s ease;
}

.carousel-fade-enter-from,
.carousel-fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

/* Fade transition for buttons */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease, transform 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: scale(0.95);
}



</style>

<script>
import MaterialInput from "@/components/MaterialInput.vue";
import Analise from "./Planejamento/components/Analise.vue";
import Diagnostico from "./Planejamento/components/Diagnostico.vue";

import Imagens from "./Planejamento/components/Imagens.vue";
import PlanoTratamento from "./Planejamento/components/PlanoTratamento.vue";
import { getImageDescription } from "@/helpers/utils";
import setNavPills from "@/assets/js/nav-pills.js";

const items = [];
var isEditing = [];

export default {
  name: "planejamento",
  props: {
    paciente: {
      type: Object,
    },
  },
  data() {
    return {
      isEditing,
      items,
      planejamentoTab: "imagens",

      isPaused: false,
      isCarouselHidden: false,
      editModeActive: false,
    };
  },
  methods: {
    getImageDescription,
    selectPlanejamentoTab(tab) {
      // Atualizar a variável de estado
      this.planejamentoTab = tab;

      // Handle carousel visibility based on selected tab
      if (tab === 'imagens') {
        // When on Documentação inicial tab, dim the carousel but don't hide it completely
        // This prevents the layout from jumping when switching tabs
      } else {
        // When on other tabs, ensure carousel is visible
        this.isCarouselHidden = false;
      }
    },

    // Métodos para a barra de progresso
    getStepClasses(step) {
      const classes = [];

      if (this.planejamentoTab === step) {
        classes.push('active');
      }

      if (this.isStepCompleted(step)) {
        classes.push('completed');
      }

      if (!this.canAccessStep(step)) {
        classes.push('disabled');
      }

      return classes;
    },

    getStepIcon(step) {
      const icons = {
        'imagens': ['fas', 'image'],
        'analise': ['fas', 'magnifying-glass'],
        'diagnostico': ['fas', 'book-medical'],
        'planoTratamento': ['fas', 'file-pen']
      };

      // Se a etapa está completa, mostra um check
      if (this.isStepCompleted(step)) {
        return ['fas', 'check'];
      }

      return icons[step] || ['fas', 'circle'];
    },

    canAccessStep(step) {
      const stepOrder = ['imagens', 'analise', 'diagnostico', 'planoTratamento'];
      const currentIndex = stepOrder.indexOf(step);

      // Sempre pode acessar a primeira etapa
      if (currentIndex === 0) return true;

      // Para outras etapas, verifica se a anterior está completa
      const previousStep = stepOrder[currentIndex - 1];
      return this.isStepCompleted(previousStep);
    },

    isStepCompleted(step) {
      switch (step) {
        case 'imagens':
          // Considera completa se tem pelo menos algumas imagens de diagnóstico
          return this.safePatientImages.length > 0;
        case 'analise':
          // Considera completa se tem análises preenchidas (simplificado)
          return this.paciente.analises && Object.keys(this.paciente.analises).length > 0;
        case 'diagnostico':
          // Considera completa se tem diagnóstico
          return this.paciente.diagnostico && this.paciente.diagnostico.trim().length > 0;
        case 'planoTratamento':
          // Considera completa se tem plano de tratamento
          return this.paciente.plano_tratamento && this.paciente.plano_tratamento.trim().length > 0;
        default:
          return false;
      }
    },
    toggleEditMode(section) {
      this.isEditing[section] = !this.isEditing[section];
    },
    /**
     * Pause the carousel of images when an image is clicked. This helps
     * prevent the user from accidentally advancing to the next image while
     * they are viewing an image.
     */
    pauseCarousel() {
      this.isPaused = true;
    },
    resumeCarousel() {
      this.isPaused = false;
    },
    /**
     * Toggle the carousel between paused and playing states
     */
    toggleCarousel() {
      this.isPaused = !this.isPaused;
    },
    /**
     * Toggle the visibility of the carousel
     */
    toggleCarouselVisibility() {
      this.isCarouselHidden = !this.isCarouselHidden;
    },
    /**
     * Truncate a description to a specified length and add ellipsis if needed
     */
    truncateDescription(text, maxLength) {
      if (!text) return '';
      if (text.length <= maxLength) return text;
      return text.substring(0, maxLength) + '...';
    },


    // Método para lidar com o evento edit-mode-active do componente PlanoTratamento
    handleEditModeActive(isActive) {
      this.editModeActive = isActive;
      // Propagar o evento para o componente pai
      this.$emit('edit-mode-active', isActive);
    },
  },
  components: {
    Analise,
    Diagnostico,
    Imagens,
    PlanoTratamento,
    MaterialInput,
  },
  computed: {
    imagens() {
      const allImages = this.paciente.imagens.filter((imagem) => imagem.dir !== "profile_pic");

      // Separar imagens de diagnóstico das regulares
      const diagnosticImages = allImages.filter(img => img.is_diagnostico === true || img.is_diagnostico === 1);
      const regularImages = allImages.filter(img => img.is_diagnostico !== true && img.is_diagnostico !== 1);

      // Ordenar imagens de diagnóstico: Extra-bucais, Intra-bucais, Radiografias
      const orderedDiagnosticImages = [];

      // 1. Extra-bucais
      const extraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('extra_');
      });
      orderedDiagnosticImages.push(...extraBucais);

      // 2. Intra-bucais
      const intraBucais = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('intra_');
      });
      orderedDiagnosticImages.push(...intraBucais);

      // 3. Radiografias (por último)
      const radiografias = diagnosticImages.filter(img => {
        const tag = img.tag_diagnostico || '';
        return tag.startsWith('radio_');
      });
      orderedDiagnosticImages.push(...radiografias);

      // Retornar imagens de diagnóstico ordenadas + imagens regulares
      return [...orderedDiagnosticImages, ...regularImages];
    },

    // Computed properties para a barra de progresso
    safePatientImages() {
      return this.paciente?.imagens?.filter(img =>
        img.is_diagnostico === true || img.is_diagnostico === 1
      ) || [];
    },

    progressPercentage() {
      const steps = ['imagens', 'analise', 'diagnostico', 'planoTratamento'];
      const completedSteps = steps.filter(step => this.isStepCompleted(step)).length;
      const currentStepIndex = steps.indexOf(this.planejamentoTab);

      // Se estamos numa etapa, conta como parcialmente completa
      const progressSteps = completedSteps + (currentStepIndex >= completedSteps ? 0.5 : 0);

      return Math.min((progressSteps / steps.length) * 100, 100);
    },
    detalhesClinicos() {
      return this.paciente.detalhes_paciente
        ? this.paciente.detalhes_paciente.filter((detalhe) => detalhe.tipo == "clinico")
        : [];
    },
    // Computed property to determine if carousel should be dimmed
    shouldDimCarousel() {
      return this.planejamentoTab === 'imagens';
    },
    ultimaFase() {
      return this.paciente.fases_tratamento[this.paciente.fases_tratamento.length - 1]
        .data_fim;
    },
  },

  mounted() {
    // Add a global reference to the component for debugging
    window.planejamentoComponent = this;

    this.$nextTick(() => {
      // Garantir que a tab inicial (documentação inicial) esteja ativa
      this.planejamentoTab = 'imagens';

      // Não inicializar o nav-pills para usar o estilo de tab azul em vez do moving-tab

      // Initialize carousel state based on current tab
      this.isPaused = false;
      this.isCarouselHidden = this.planejamentoTab === 'imagens'; // 'imagens' é o valor da variável, mas representa a tab "Documentação inicial"

      // Add event listener to watch for tab changes and update carousel state
      this.$watch('planejamentoTab', (newTab) => {
        // When switching to Documentação inicial tab, dim the carousel
        if (newTab === 'imagens') {
          // Optional: automatically hide carousel when on Documentação inicial tab
          // this.isCarouselHidden = true;
        } else {
          // When switching away from Documentação inicial tab, ensure carousel is visible
          this.isCarouselHidden = false;
        }
      });
    });
  },
  beforeUnmount() {},
};
</script>
